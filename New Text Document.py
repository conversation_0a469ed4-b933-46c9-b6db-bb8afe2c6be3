#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import pyodbc
import asyncio
import threading
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes

# إعدادات البوت
BOT_TOKEN = "8159472977:AAHnzuRjZ_PoaHBgBgzLoacTpzIotxg9Jz8"
ADMIN_ID = 1107000748

# إعدادات قاعدة البيانات SQL Server
DB_CONFIG = {
    'server': 'terraaa.ddns.net,4100',
    'database': 'terraedu01',
    'username': 'sa',
    'password': '@a123admin4',
    'driver': '{ODBC Driver 17 for SQL Server}',
    'trusted_connection': 'no'
}

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# متغيرات عامة
db_connection = None
last_student_count = 0
monitoring_active = False
bot_application = None
user_states = {}  # لحفظ حالات المستخدمين
authorized_users = set()  # مجموعة المستخدمين المفوضين
pending_requests = {}  # طلبات التفويض المعلقة

def get_connection_string():
    """إنشاء connection string لـ SQL Server"""
    return f"DRIVER={DB_CONFIG['driver']};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']};TrustServerCertificate=yes;"

def connect_to_database():
    """الاتصال بقاعدة البيانات SQL Server"""
    global db_connection
    try:
        print("⏳ محاولة الاتصال بقاعدة البيانات SQL Server...")
        print(f"🌐 الخادم: {DB_CONFIG['server']}")
        print(f"👤 المستخدم: {DB_CONFIG['username']}")
        print(f"🗄️ قاعدة البيانات: {DB_CONFIG['database']}")
        
        connection_string = get_connection_string()
        db_connection = pyodbc.connect(connection_string, timeout=30)
        print("✅ تم الاتصال بقاعدة البيانات بنجاح!")

        # إنشاء جداول التلجرام
        create_telegram_tables()

        # البوت مفتوح للجميع - لا حاجة لتحميل صلاحيات

        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        print("⚠️ البوت سيعمل بدون قاعدة بيانات")
        return False

# تم إزالة نظام الصلاحيات - البوت مفتوح للجميع

def create_telegram_tables():
    """إنشاء جداول التلجرام في SQL Server"""
    if not db_connection:
        return False
    
    try:
        cursor = db_connection.cursor()
        
        # إنشاء جدول المستخدمين المفوضين
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='telegram_bot_users' AND xtype='U')
            CREATE TABLE telegram_bot_users (
                id INT IDENTITY(1,1) PRIMARY KEY,
                telegram_id BIGINT UNIQUE NOT NULL,
                username NVARCHAR(255),
                first_name NVARCHAR(255),
                last_name NVARCHAR(255),
                authorized_at DATETIME DEFAULT GETDATE(),
                is_active BIT DEFAULT 1
            )
        """)
        
        # إنشاء جدول طلبات التفويض
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='telegram_bot_requests' AND xtype='U')
            CREATE TABLE telegram_bot_requests (
                id INT IDENTITY(1,1) PRIMARY KEY,
                telegram_id BIGINT NOT NULL,
                username NVARCHAR(255),
                first_name NVARCHAR(255),
                last_name NVARCHAR(255),
                requested_at DATETIME DEFAULT GETDATE(),
                status NVARCHAR(20) DEFAULT 'pending'
            )
        """)
        
        # إضافة الأدمن
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM telegram_bot_users WHERE telegram_id = ?)
            INSERT INTO telegram_bot_users (telegram_id, username, first_name, is_active)
            VALUES (?, 'admin', 'Admin', 1)
            ELSE
            UPDATE telegram_bot_users SET is_active = 1, authorized_at = GETDATE() WHERE telegram_id = ?
        """, (ADMIN_ID, ADMIN_ID, ADMIN_ID))
        
        db_connection.commit()
        print("✅ تم إنشاء جداول التلجرام وإضافة الأدمن")
        
        # عرض المستخدمين المفوضين
        cursor.execute("SELECT telegram_id, first_name FROM telegram_bot_users WHERE is_active = 1")
        users = cursor.fetchall()
        print(f"👥 المستخدمين المفوضين: {len(users)}")
        for user in users:
            print(f"  - {user[0]} ({user[1]})")
            authorized_users.add(user[0])
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول التلجرام: {e}")
        return False

def setup_telegram_tables():
    """إنشاء جداول التلجرام إذا لم تكن موجودة"""
    if not db_connection:
        return

    try:
        cursor = db_connection.cursor()

        # إنشاء جدول المستخدمين المفوضين
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='telegram_bot_users' AND xtype='U')
            CREATE TABLE telegram_bot_users (
                id INT IDENTITY(1,1) PRIMARY KEY,
                telegram_id BIGINT NOT NULL UNIQUE,
                username NVARCHAR(255),
                first_name NVARCHAR(255),
                last_name NVARCHAR(255),
                authorized_at DATETIME DEFAULT GETDATE(),
                is_active BIT DEFAULT 1
            )
        """)

        # إنشاء جدول المستقبلين للإشعارات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NotificationRecipients' AND xtype='U')
            CREATE TABLE NotificationRecipients (
                id INT IDENTITY(1,1) PRIMARY KEY,
                RecipientType NVARCHAR(50) NOT NULL,
                ChatId BIGINT NOT NULL UNIQUE,
                RecipientName NVARCHAR(255),
                Description NVARCHAR(500),
                IsActive BIT DEFAULT 1,
                AddDate DATETIME DEFAULT GETDATE(),
                AddedBy NVARCHAR(255),
                LastNotificationDate DATETIME,
                NotificationCount INT DEFAULT 0,
                ReceiveNasrBranch BIT DEFAULT 1,
                ReceiveTajammuBranch BIT DEFAULT 1,
                ReceiveReports BIT DEFAULT 1
            )
        """)

        # إنشاء جدول تتبع الإشعارات المرسلة
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NotificationLog' AND xtype='U')
            CREATE TABLE NotificationLog (
                id INT IDENTITY(1,1) PRIMARY KEY,
                ChatId BIGINT NOT NULL,
                StudentId INT,
                StudentName NVARCHAR(255),
                StudentCode NVARCHAR(50),
                NotificationType NVARCHAR(100),
                MessageContent NTEXT,
                SentDate DATETIME DEFAULT GETDATE(),
                DeliveryStatus NVARCHAR(50) DEFAULT 'Sent',
                ErrorMessage NVARCHAR(500)
            )
        """)

        # إضافة الأدمن إذا لم يكن موجود
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM telegram_bot_users WHERE telegram_id = ?)
            INSERT INTO telegram_bot_users (telegram_id, username, first_name, authorized_at, is_active)
            VALUES (?, 'admin', 'Admin', GETDATE(), 1)
        """, (ADMIN_ID, ADMIN_ID))

        # إضافة الأدمن كمستقبل للإشعارات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM NotificationRecipients WHERE ChatId = ?)
            INSERT INTO NotificationRecipients (RecipientType, ChatId, RecipientName, Description, AddedBy)
            VALUES ('User', ?, 'Admin', 'System Administrator', 'System')
        """, (ADMIN_ID, ADMIN_ID))

        db_connection.commit()
        print("✅ تم إنشاء جداول التلجرام وجدول المستقبلين")

    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول التلجرام: {e}")

def load_authorized_users():
    """تحميل المستخدمين المفوضين من قاعدة البيانات"""
    global authorized_users

    if not db_connection:
        return

    try:
        cursor = db_connection.cursor()
        cursor.execute("SELECT telegram_id FROM telegram_bot_users WHERE is_active = 1")
        users = cursor.fetchall()

        authorized_users = {ADMIN_ID}  # الأدمن دائماً مفوض
        for user in users:
            authorized_users.add(user[0])

        print(f"📱 المستخدمين المفوضين: {len(authorized_users)}")
        for user_id in authorized_users:
            if user_id == ADMIN_ID:
                print(f"  - {user_id} (Admin)")
            else:
                print(f"  - {user_id}")

    except Exception as e:
        print(f"❌ خطأ في تحميل المستخدمين المفوضين: {e}")

def add_user_to_recipients(telegram_id, username, first_name, recipient_type):
    """إضافة مستخدم جديد لجدول مستقبلي الإشعارات"""
    if not db_connection:
        return False

    try:
        cursor = db_connection.cursor()

        # التحقق من وجود المستخدم
        cursor.execute("SELECT id FROM NotificationRecipients WHERE ChatId = ?", (telegram_id,))
        existing = cursor.fetchone()

        if existing:
            # تحديث المستخدم الموجود
            cursor.execute("""
                UPDATE NotificationRecipients
                SET IsActive = 1, RecipientName = ?, AddDate = GETDATE()
                WHERE ChatId = ?
            """, (first_name or 'Unknown', telegram_id))
        else:
            # إضافة مستخدم جديد
            cursor.execute("""
                INSERT INTO NotificationRecipients
                (RecipientType, ChatId, RecipientName, Description, AddedBy, IsActive)
                VALUES (?, ?, ?, ?, ?, 1)
            """, (recipient_type, telegram_id, first_name or 'Unknown',
                  f"Auto-added user: @{username or 'unknown'}", 'System'))

        db_connection.commit()
        print(f"✅ تم إضافة المستخدم {telegram_id} لجدول المستقبلين")
        return True

    except Exception as e:
        print(f"❌ خطأ في إضافة المستخدم لجدول المستقبلين: {e}")
        return False

def get_students_from_db():
    """جلب الطلاب من قاعدة البيانات مع JOINs للحصول على الأسماء الحقيقية"""
    if not db_connection:
        return []

    try:
        cursor = db_connection.cursor()

        # جلب الطلاب مع JOINs للحصول على أسماء المؤسسات والفروع والدرجات والمجموعات
        cursor.execute("""
            SELECT TOP 20
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender, s.created_by,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at, s.updated_at,
                i.institution_name,
                b.branch_name,
                g.grade_name,
                gr.group_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            LEFT JOIN [dbo].[StudentGroups] sg ON s.id = sg.student_id
            LEFT JOIN [dbo].[Groups] gr ON sg.group_id = gr.id
            WHERE s.is_active = 1 AND s.is_deleted = 0
            ORDER BY s.created_at DESC, s.student_code
        """)

        students = cursor.fetchall()

        if students:
            print(f"✅ تم العثور على {len(students)} طالب مع البيانات المربوطة")

            # الحصول على أسماء الأعمدة
            columns = [column[0] for column in cursor.description]
            print(f"📋 أعمدة الجدول مع JOINs: {columns}")

            result = []
            for student in students:
                student_dict = dict(zip(columns, student))
                result.append(student_dict)
                # طباعة أول طالب للتأكد من البيانات
                if len(result) == 1:
                    print(f"📊 مثال على البيانات مع JOINs: {student_dict}")

            return result
        else:
            print("⚠️ لا توجد طلاب نشطين في قاعدة البيانات")
            return []

    except Exception as e:
        print(f"❌ خطأ في جلب بيانات الطلاب مع JOINs: {e}")
        return []

def get_latest_student():
    """جلب آخر طالب مضاف"""
    if not db_connection:
        return None

    try:
        cursor = db_connection.cursor()

        cursor.execute("""
            SELECT TOP 1
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender, s.created_by, s.updated_by,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at, s.updated_at,
                i.institution_name,
                b.branch_name,
                g.grade_name,
                gr.group_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            LEFT JOIN [dbo].[StudentGroups] sg ON s.id = sg.student_id
            LEFT JOIN [dbo].[Groups] gr ON sg.group_id = gr.id
            WHERE s.is_active = 1 AND s.is_deleted = 0
            ORDER BY s.created_at DESC, s.id DESC
        """)

        student = cursor.fetchone()

        if student:
            columns = [column[0] for column in cursor.description]
            return dict(zip(columns, student))

        return None

    except Exception as e:
        print(f"❌ خطأ في جلب آخر طالب: {e}")
        return None

def get_active_recipients():
    """جلب قائمة المستقبلين النشطين من قاعدة البيانات"""
    if not db_connection:
        return []

    try:
        cursor = db_connection.cursor()
        cursor.execute("""
            SELECT ChatId, RecipientName, RecipientType, ReceiveNasrBranch, ReceiveTajammuBranch, ReceiveReports
            FROM NotificationRecipients
            WHERE IsActive = 1
            ORDER BY AddDate
        """)

        recipients = cursor.fetchall()
        columns = [column[0] for column in cursor.description]

        result = []
        for recipient in recipients:
            recipient_dict = dict(zip(columns, recipient))
            result.append(recipient_dict)

        return result

    except Exception as e:
        print(f"❌ خطأ في جلب المستقبلين: {e}")
        return []

def log_notification(chat_id, student_data, message_content, status, error_message=None):
    """تسجيل الإشعار في قاعدة البيانات"""
    if not db_connection:
        return

    try:
        cursor = db_connection.cursor()

        student_id = student_data.get('id') if student_data else None
        student_name = student_data.get('student_name') if student_data else None
        student_code = student_data.get('student_code') if student_data else None

        cursor.execute("""
            INSERT INTO NotificationLog
            (ChatId, StudentId, StudentName, StudentCode, NotificationType, MessageContent, DeliveryStatus, ErrorMessage)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (chat_id, student_id, student_name, student_code, 'NewStudent', message_content, status, error_message))

        # تحديث عداد الإشعارات للمستقبل
        cursor.execute("""
            UPDATE NotificationRecipients
            SET NotificationCount = NotificationCount + 1, LastNotificationDate = GETDATE()
            WHERE ChatId = ?
        """, (chat_id,))

        db_connection.commit()

    except Exception as e:
        print(f"❌ خطأ في تسجيل الإشعار: {e}")

def format_new_student_notification(student):
    """تنسيق إشعار طالب جديد بناءً على البيانات الكاملة من الصورة"""
    if not student:
        return "❌ لا توجد بيانات للطالب الجديد"

    # البيانات الأساسية
    student_name = student.get('student_name', 'غير محدد')
    student_code = student.get('student_code', 'غير محدد')
    student_phone = student.get('student_phone', 'غير محدد')
    father_phone = student.get('father_phone', 'غير محدد')
    mother_phone = student.get('mother_phone', 'غير محدد')
    gender = student.get('gender', 'غير محدد')

    # البيانات الأكاديمية
    institution_name = student.get('institution_name', 'غير محدد')
    branch_name = student.get('branch_name', 'غير محدد')
    grade_name = student.get('grade_name', 'غير محدد')
    group_name = student.get('group_name', 'غير محدد')

    # بيانات إضافية
    created_at = student.get('created_at', 'غير محدد')
    updated_at = student.get('updated_at', 'غير محدد')
    created_by = student.get('created_by', 'غير محدد')
    updated_by = student.get('updated_by', 'غير محدد')

    # الإعفاءات
    is_exempt_from_fees = student.get('is_exempt_from_fees', False)
    is_exempt_from_books = student.get('is_exempt_from_books', False)

    # تنسيق الرسالة بدون Markdown لتجنب أخطاء التحليل
    message = "🎉 طالب جديد تم تسجيله! 🎉\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

    message += f"👤 الاسم: {student_name}\n"
    message += f"🆔 كود الطالب: {student_code}\n"
    message += f"👫 الجنس: {gender}\n\n"

    message += f"📱 أرقام الهواتف:\n"
    message += f"   • الطالب: {student_phone}\n"
    message += f"   • الأب: {father_phone}\n"
    message += f"   • الأم: {mother_phone}\n\n"

    message += f"🏫 البيانات الأكاديمية:\n"
    message += f"   • المؤسسة: {institution_name}\n"
    message += f"   • الفرع: {branch_name}\n"
    message += f"   • الصف: {grade_name}\n"
    message += f"   • المجموعة: {group_name}\n\n"

    # الإعفاءات
    if is_exempt_from_fees or is_exempt_from_books:
        message += f"💰 الإعفاءات:\n"
        if is_exempt_from_fees:
            message += f"   ✅ معفى من الرسوم\n"
        if is_exempt_from_books:
            message += f"   ✅ معفى من الكتب\n"
        message += "\n"

    message += f"📅 تاريخ التسجيل: {created_at}\n"
    message += f"👤 أضيف بواسطة: {created_by}\n\n"

    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
    message += "🔔 إشعار تلقائي من نظام تيرا التعليمي"

    return message

def create_student_action_buttons(student):
    """إنشاء أزرار الإجراءات للطالب الجديد"""
    from telegram import InlineKeyboardButton, InlineKeyboardMarkup

    student_phone = student.get('student_phone', '')
    student_code = student.get('student_code', '')
    student_name = student.get('student_name', '')

    # تنظيف وتنسيق رقم الهاتف
    clean_phone = clean_phone_number(student_phone)

    # تنسيق رسالة الواتساب بالإنجليزية لتجنب مشاكل الترميز
    whatsapp_message = f"""🎉 Congratulations! You have been registered in Terra Educational System

👤 Name: {student_name}
🆔 Student Code: {student_code}
📱 Phone: {student_phone}

🏫 Welcome to Terra Educational Family!
📚 We wish you success in your educational journey

🔔 You will receive notifications about schedules and activities soon"""

    # ترميز الرسالة للواتساب مع UTF-8
    import urllib.parse
    encoded_message = urllib.parse.quote(whatsapp_message, safe='')

    keyboard = [
        [
            InlineKeyboardButton(
                "📱 إرسال واتساب للطالب",
                callback_data=f"send_whatsapp_{student_code}"
            )
        ],
        [
            InlineKeyboardButton(
                "🆔 إنشاء كارنيه",
                callback_data=f"create_card_{student_code}"
            )
        ]
    ]

    return InlineKeyboardMarkup(keyboard)

def clean_phone_number(phone):
    """تنظيف وتنسيق رقم الهاتف لإضافة كود الدولة"""
    if not phone:
        return ""

    # إزالة المسافات والرموز
    phone = str(phone).strip().replace(' ', '').replace('-', '').replace('(', '').replace(')', '')

    # إذا كان الرقم يبدأ بـ 01، أضف كود مصر
    if phone.startswith('01'):
        phone = '2' + phone
    # إذا كان يبدأ بـ 1 فقط، أضف 20
    elif phone.startswith('1') and len(phone) == 10:
        phone = '20' + phone
    # إذا لم يبدأ بـ +، أضفه
    if not phone.startswith('+'):
        phone = '+' + phone

    return phone

def create_main_menu_keyboard():
    """إنشاء لوحة الأزرار الرئيسية"""
    from telegram import InlineKeyboardButton, InlineKeyboardMarkup

    keyboard = [
        [
            InlineKeyboardButton("📱 البحث بالتليفون", callback_data="menu_phone_search"),
            InlineKeyboardButton("🆔 استخراج كارنيه", callback_data="menu_card_extract")
        ],
        [
            InlineKeyboardButton("📊 عرض الطلاب", callback_data="menu_students"),
            InlineKeyboardButton("📈 الإحصائيات", callback_data="menu_stats")
        ],
        [
            InlineKeyboardButton("🔍 البحث العام", callback_data="menu_search"),
            InlineKeyboardButton("👤 آخر طالب", callback_data="menu_latest")
        ],
        [
            InlineKeyboardButton("📋 جميع الطلاب", callback_data="menu_all_students"),
            InlineKeyboardButton("🆔 معرف المحادثة", callback_data="menu_chat_id")
        ]
    ]

    return InlineKeyboardMarkup(keyboard)

def create_enhanced_student_card_image(student):
    """إنشاء كارنيه احترافي جداً للطالب مع QR Code كبير"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import qrcode
        import io

        # إنشاء QR Code كبير
        qr = qrcode.QRCode(
            version=2,  # حجم أكبر
            box_size=15,  # مربعات أكبر
            border=4
        )
        qr.add_data(student.get('student_code', ''))
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="#1a365d", back_color="white")

        # إنشاء الكارنيه بحجم أكبر
        card_width, card_height = 800, 500
        card = Image.new('RGB', (card_width, card_height), color='white')
        draw = ImageDraw.Draw(card)

        # خلفية متدرجة احترافية
        # الجزء العلوي - أزرق داكن
        draw.rectangle([0, 0, card_width, 100], fill='#1a365d')
        # شريط ذهبي
        draw.rectangle([0, 100, card_width, 120], fill='#d4af37')
        # الجزء السفلي - أزرق فاتح
        draw.rectangle([0, card_height-60, card_width, card_height], fill='#2d5aa0')

        # إضافة خطوط زخرفية
        for i in range(0, card_width, 50):
            draw.line([i, 120, i+20, 140], fill='#f0f8ff', width=2)

        # محاولة تحميل خط عربي
        try:
            # خطوط عربية احترافية
            font_title = ImageFont.truetype("arial.ttf", 32)
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_medium = ImageFont.truetype("arial.ttf", 18)
            font_small = ImageFont.truetype("arial.ttf", 14)
        except:
            font_title = ImageFont.load_default()
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()

        # عنوان الكارنيه
        draw.text((30, 25), "TERRA EDUCATIONAL SYSTEM", fill='white', font=font_title)
        draw.text((30, 60), "Student ID Card", fill='#d4af37', font=font_medium)

        # بيانات الطالب بالإنجليزية لتجنب مشاكل العربية
        student_name = student.get('student_name', 'Unknown')
        student_code = student.get('student_code', 'Unknown')
        grade_name = student.get('grade_name', 'Unknown')
        branch_name = student.get('branch_name', 'Unknown')
        institution_name = student.get('institution_name', 'Unknown')

        # تحويل النصوص العربية للإنجليزية إذا أمكن
        name_en = translate_to_english(student_name)
        grade_en = translate_to_english(grade_name)
        branch_en = translate_to_english(branch_name)
        institution_en = translate_to_english(institution_name)

        # منطقة البيانات
        y_start = 160

        # اسم الطالب
        draw.text((30, y_start), "Student Name:", fill='#1a365d', font=font_medium)
        draw.text((30, y_start + 25), name_en, fill='#2d5aa0', font=font_large)

        # كود الطالب
        draw.text((30, y_start + 60), "Student Code:", fill='#1a365d', font=font_medium)
        draw.text((30, y_start + 85), student_code, fill='#d4af37', font=font_large)

        # الصف
        draw.text((30, y_start + 120), "Grade:", fill='#1a365d', font=font_medium)
        draw.text((30, y_start + 145), grade_en, fill='#2d5aa0', font=font_medium)

        # الفرع
        draw.text((30, y_start + 180), "Branch:", fill='#1a365d', font=font_medium)
        draw.text((30, y_start + 205), branch_en, fill='#2d5aa0', font=font_medium)

        # المؤسسة
        draw.text((30, y_start + 240), "Institution:", fill='#1a365d', font=font_medium)
        draw.text((30, y_start + 265), institution_en, fill='#2d5aa0', font=font_medium)

        # إضافة QR Code كبير
        qr_size = 180  # حجم أكبر بكثير
        qr_img = qr_img.resize((qr_size, qr_size))

        # موضع QR Code
        qr_x = card_width - qr_size - 40
        qr_y = y_start + 20

        # إضافة خلفية بيضاء للـ QR Code
        draw.rectangle([qr_x-10, qr_y-10, qr_x+qr_size+10, qr_y+qr_size+10],
                      fill='white', outline='#1a365d', width=3)

        card.paste(qr_img, (qr_x, qr_y))

        # نص تحت QR Code
        draw.text((qr_x + 20, qr_y + qr_size + 15), "Scan for Details",
                 fill='#1a365d', font=font_small)

        # إضافة شعار أو نص في الأسفل
        draw.text((30, card_height - 40), "Terra Educational System © 2025",
                 fill='white', font=font_small)
        draw.text((card_width - 200, card_height - 40), "www.terra-edu.com",
                 fill='#d4af37', font=font_small)

        # حفظ الصورة في الذاكرة
        img_buffer = io.BytesIO()
        card.save(img_buffer, format='PNG', quality=95)
        img_buffer.seek(0)

        return img_buffer

    except Exception as e:
        print(f"❌ خطأ في إنشاء الكارنيه المحسن: {e}")
        return None

def translate_to_english(arabic_text):
    """ترجمة بسيطة للنصوص العربية الشائعة"""
    # التحقق من أن النص ليس None
    if arabic_text is None:
        return "Unknown"

    # تحويل إلى نص
    arabic_text = str(arabic_text)

    translations = {
        'علي': 'Ali',
        'حسن': 'Hassan',
        'جابر': 'Jaber',
        'محمد': 'Mohamed',
        'أحمد': 'Ahmed',
        'فاطمة': 'Fatma',
        'عائشة': 'Aisha',
        'خديجة': 'Khadija',
        'الصف الأول': 'First Grade',
        'الصف الثاني': 'Second Grade',
        'الصف الثالث': 'Third Grade',
        'الصف الرابع': 'Fourth Grade',
        'الصف الخامس': 'Fifth Grade',
        'الصف السادس': 'Sixth Grade',
        'فرع النصر': 'Nasr Branch',
        'فرع التجمع': 'Tagammu Branch',
        'فرع المعادي': 'Maadi Branch',
        'مؤسسة تيرا': 'Terra Institution',
        'مؤسسة التعليم': 'Education Institution'
    }

    # البحث عن ترجمة
    for arabic, english in translations.items():
        if arabic in arabic_text:
            return english

    # إذا لم توجد ترجمة، إرجاع النص كما هو
    return arabic_text

def create_student_card_image(student):
    """إنشاء صورة كارنيه للطالب مع QR Code"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import qrcode
        import io

        # إنشاء QR Code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(student.get('student_code', ''))
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # إنشاء الكارنيه
        card_width, card_height = 600, 400
        card = Image.new('RGB', (card_width, card_height), color='white')
        draw = ImageDraw.Draw(card)

        # إضافة خلفية ملونة
        draw.rectangle([0, 0, card_width, 80], fill='#2E86AB')
        draw.rectangle([0, card_height-50, card_width, card_height], fill='#2E86AB')

        # إضافة النصوص (استخدام خط افتراضي)
        try:
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_medium = ImageFont.truetype("arial.ttf", 18)
            font_small = ImageFont.truetype("arial.ttf", 14)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()

        # عنوان الكارنيه
        draw.text((20, 20), "نظام تيرا التعليمي", fill='white', font=font_large)
        draw.text((20, 50), "Student ID Card", fill='white', font=font_small)

        # بيانات الطالب
        y_pos = 120
        student_name = student.get('student_name', 'غير محدد')
        student_code = student.get('student_code', 'غير محدد')
        grade_name = student.get('grade_name', 'غير محدد')
        branch_name = student.get('branch_name', 'غير محدد')

        draw.text((20, y_pos), f"الاسم: {student_name}", fill='black', font=font_medium)
        draw.text((20, y_pos + 30), f"كود الطالب: {student_code}", fill='black', font=font_medium)
        draw.text((20, y_pos + 60), f"الصف: {grade_name}", fill='black', font=font_medium)
        draw.text((20, y_pos + 90), f"الفرع: {branch_name}", fill='black', font=font_medium)

        # إضافة QR Code
        qr_size = 120
        qr_img = qr_img.resize((qr_size, qr_size))
        card.paste(qr_img, (card_width - qr_size - 20, y_pos))

        # حفظ الصورة في الذاكرة
        img_buffer = io.BytesIO()
        card.save(img_buffer, format='PNG')
        img_buffer.seek(0)

        return img_buffer

    except Exception as e:
        print(f"❌ خطأ في إنشاء الكارنيه: {e}")
        return None

async def monitor_students_table():
    """مراقبة جدول الطلاب لإرسال إشعارات عند إضافة طلاب جدد"""
    global last_student_count, monitoring_active

    if not db_connection or not bot_application:
        return

    monitoring_active = True
    print("🔍 بدء مراقبة جدول الطلاب...")

    # الحصول على العدد الحالي للطلاب
    try:
        cursor = db_connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM [dbo].[Students] WHERE is_active = 1 AND is_deleted = 0")
        last_student_count = cursor.fetchone()[0]
        print(f"📊 العدد الحالي للطلاب: {last_student_count}")
    except Exception as e:
        print(f"❌ خطأ في الحصول على عدد الطلاب: {e}")
        return

    while monitoring_active:
        try:
            await asyncio.sleep(10)  # فحص كل 10 ثواني

            cursor = db_connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM [dbo].[Students] WHERE is_active = 1 AND is_deleted = 0")
            current_count = cursor.fetchone()[0]

            if current_count > last_student_count:
                print(f"🎉 طالب جديد! العدد الجديد: {current_count}")

                # جلب الطالب الجديد
                latest_student = get_latest_student()

                if latest_student:
                    # إرسال إشعار لجميع المستقبلين النشطين
                    notification_message = format_new_student_notification(latest_student)

                    # جلب قائمة المستقبلين النشطين
                    recipients = get_active_recipients()

                    for recipient in recipients:
                        chat_id = recipient['ChatId']
                        try:
                            # إنشاء أزرار للواتساب والكارنيه
                            keyboard = create_student_action_buttons(latest_student)

                            await bot_application.bot.send_message(
                                chat_id=chat_id,
                                text=notification_message,
                                reply_markup=keyboard
                            )
                            print(f"✅ تم إرسال إشعار للمستقبل {chat_id}")

                            # تسجيل الإشعار في قاعدة البيانات
                            log_notification(chat_id, latest_student, notification_message, 'Sent')

                        except Exception as e:
                            print(f"❌ خطأ في إرسال إشعار للمستقبل {chat_id}: {e}")
                            log_notification(chat_id, latest_student, notification_message, 'Failed', str(e))

                last_student_count = current_count

        except Exception as e:
            print(f"❌ خطأ في مراقبة الجدول: {e}")
            await asyncio.sleep(30)  # انتظار أطول عند حدوث خطأ

def format_student_data(student):
    """تنسيق بيانات الطالب للعرض"""
    if not student:
        return "لا توجد بيانات"
    
    message = "👤 بيانات الطالب\n\n"
    
    # عرض جميع البيانات المتاحة
    for key, value in student.items():
        if value is not None and str(value).strip():
            message += f"📋 {key}: {value}\n"
    
    return message

def format_students_list(students):
    """تنسيق قائمة الطلاب بشكل جميل ومنسق"""
    if not students:
        return "❌ لا توجد بيانات طلاب في قاعدة البيانات"

    message = f"🎓 **قائمة الطلاب** ({len(students)} طالب)\n"
    message += f"{'='*50}\n\n"

    for i, student in enumerate(students[:10], 1):
        # استخدام البيانات مع JOINs للأسماء الحقيقية
        student_code = student.get('student_code', 'غير محدد')
        student_name = student.get('student_name', 'غير محدد')
        student_phone = student.get('student_phone', 'غير محدد')
        father_phone = student.get('father_phone', 'غير محدد')
        mother_phone = student.get('mother_phone', 'غير محدد')
        gender = student.get('gender', 'غير محدد')

        # الأسماء الحقيقية من JOINs
        institution_name = student.get('institution_name', 'غير محدد')
        branch_name = student.get('branch_name', 'غير محدد')
        grade_name = student.get('grade_name', 'غير محدد')
        group_name = student.get('group_name', 'غير محدد')

        # معلومات الإعفاءات
        is_exempt_from_fees = student.get('is_exempt_from_fees', False)
        is_exempt_from_books = student.get('is_exempt_from_books', False)

        # تاريخ الإنشاء
        created_at = student.get('created_at', 'غير محدد')

        message += f"{i}. 👤 {student_name}\n"
        message += f"   🆔 كود الطالب: {student_code}\n"
        message += f"   📱 هاتف الطالب: {student_phone}\n"
        message += f"   📞 هاتف الأب: {father_phone}\n"
        message += f"   📱 هاتف الأم: {mother_phone}\n"
        message += f"   👫 الجنس: {gender}\n"
        message += f"   🏢 المؤسسة: {institution_name}\n"
        message += f"   � الفرع: {branch_name}\n"
        message += f"   📚 الصف: {grade_name}\n"
        message += f"   👥 المجموعة: {group_name}\n"

        # إضافة معلومات الإعفاءات
        exemptions = []
        if is_exempt_from_fees:
            exemptions.append("الرسوم")
        if is_exempt_from_books:
            exemptions.append("الكتب")

        if exemptions:
            message += f"   💰 معفى من: {', '.join(exemptions)}\n"

        message += "\n"

    if len(students) > 10:
        message += f"... و {len(students) - 10} طالب آخر"

    return message

def get_statistics():
    """حساب الإحصائيات"""
    if not db_connection:
        return {'total': 0, 'tables': 0, 'status': 'غير متصل'}
    
    try:
        cursor = db_connection.cursor()
        
        # عدد الجداول
        cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'")
        table_count = cursor.fetchone()[0]
        
        # إحصائيات الطلاب
        cursor.execute("SELECT COUNT(*) FROM [dbo].[Students] WHERE is_active = 1 AND is_deleted = 0")
        active_students = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM [dbo].[Students]")
        total_students = cursor.fetchone()[0]

        # إحصائيات المعلمين
        cursor.execute("SELECT COUNT(*) FROM [dbo].[Teachers] WHERE is_active = 1 AND is_deleted = 0")
        active_teachers = cursor.fetchone()[0]

        # إحصائيات المؤسسات والفروع
        cursor.execute("SELECT COUNT(*) FROM [dbo].[Institutions]")
        institutions_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM [dbo].[Branches]")
        branches_count = cursor.fetchone()[0]

        # إحصائيات الصفوف والمجموعات
        cursor.execute("SELECT COUNT(*) FROM [dbo].[Grades]")
        grades_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM [dbo].[Groups]")
        groups_count = cursor.fetchone()[0]

        # إحصائيات الإشعارات
        cursor.execute("SELECT COUNT(*) FROM [dbo].[NotificationRecipients] WHERE IsActive = 1")
        active_recipients = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM [dbo].[NotificationLog]")
        total_notifications = cursor.fetchone()[0]

        return {
            'active_students': active_students,
            'total_students': total_students,
            'active_teachers': active_teachers,
            'institutions_count': institutions_count,
            'branches_count': branches_count,
            'grades_count': grades_count,
            'groups_count': groups_count,
            'active_recipients': active_recipients,
            'total_notifications': total_notifications,
            'tables': table_count,
            'status': 'متصل'
        }
        
    except Exception as e:
        return {'total': 0, 'tables': 0, 'status': f'خطأ: {e}'}

def format_statistics(stats):
    """تنسيق الإحصائيات الحقيقية"""
    message = "📊 **إحصائيات نظام تيرا التعليمي**\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

    message += f"🗄️ **حالة الاتصال:** {stats.get('status', 'غير معروف')}\n"
    message += f"🌐 **الخادم:** terraaa.ddns.net:4100\n"
    message += f"�️ **قاعدة البيانات:** terraedu01\n\n"

    message += f"👥 **الطلاب:**\n"
    message += f"   • النشطين: {stats.get('active_students', 0)} طالب\n"
    message += f"   • الإجمالي: {stats.get('total_students', 0)} طالب\n\n"

    message += f"👨‍🏫 **المعلمين:**\n"
    message += f"   • النشطين: {stats.get('active_teachers', 0)} معلم\n\n"

    message += f"� **البيانات الأكاديمية:**\n"
    message += f"   • المؤسسات: {stats.get('institutions_count', 0)}\n"
    message += f"   • الفروع: {stats.get('branches_count', 0)}\n"
    message += f"   • الصفوف: {stats.get('grades_count', 0)}\n"
    message += f"   • المجموعات: {stats.get('groups_count', 0)}\n\n"

    message += f"� **الإشعارات:**\n"
    message += f"   • المستقبلين النشطين: {stats.get('active_recipients', 0)}\n"
    message += f"   • إجمالي الإشعارات: {stats.get('total_notifications', 0)}\n\n"

    message += f"� **النظام:**\n"
    message += f"   • عدد الجداول: {stats.get('tables', 0)}\n"

    message += "\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    return message

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر البدء"""
    user = update.effective_user
    
    db_status = "متصلة ✅" if db_connection else "غير متصلة ❌"
    
    welcome_message = f"""
🎓 مرحباً {user.first_name} في بوت تيرا التعليمي

🗄️ حالة قاعدة البيانات: {db_status}
🌐 الخادم: terraaa.ddns.net:4100
📊 قاعدة البيانات: terraedu01
💾 نوع قاعدة البيانات: SQL Server

📊 البيانات الحالية:
   • 72 طالب نشط
   • 22 معلم
   • 7 فروع
   • 11 صف دراسي
   • 25 مجموعة

🎯 الأوامر الجديدة:
   • /phone [رقم] - البحث بالتليفون مع واتساب
   • /card [كود] - استخراج كارنيه احترافي

للوصول إلى خدمات البوت، يرجى استخدام الأمر:
/terra

سيتم إرسال طلب تفويض للإدارة للموافقة على وصولك.
"""
    
    await update.message.reply_text(welcome_message)

async def terra(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر terra للوصول للخدمات - مفتوح للجميع"""
    user = update.effective_user

    # البوت مفتوح للجميع - عرض الخدمات مباشرة
    db_status = "متصلة ✅" if db_connection else "غير متصلة ❌"

    welcome_message = f"""
🎉 مرحباً {user.first_name}!

أنت مفوض للوصول إلى نظام تيرا التعليمي.

🗄️ حالة قاعدة البيانات: {db_status}
🌐 الخادم: terraaa.ddns.net:4100
📊 الطلاب النشطين: 72 طالب

استخدم الأزرار أدناه للوصول السريع للخدمات:
"""

    # إنشاء لوحة أزرار جميلة
    keyboard = create_main_menu_keyboard()

    await update.message.reply_text(
        welcome_message,
        reply_markup=keyboard
    )

async def students(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض قائمة الطلاب - مفتوح للجميع"""
    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات
    
    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة. تحقق من الاتصال بالخادم.")
        return
    
    students_data = get_students_from_db()
    message = format_students_list(students_data)
    await update.message.reply_text(message)

async def stats(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض الإحصائيات - مفتوح للجميع"""
    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات
    
    statistics = get_statistics()
    message = format_statistics(statistics)
    await update.message.reply_text(message)

async def latest(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """عرض آخر طالب مسجل"""
    user_id = update.effective_user.id
    
    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات
    
    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة.")
        return
    
    students_data = get_students_from_db()
    latest_student = students_data[0] if students_data else None
    message = format_student_data(latest_student)
    await update.message.reply_text(message)

async def search(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """البحث عن طالب"""
    user_id = update.effective_user.id

    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات

    if not context.args:
        await update.message.reply_text("استخدم الأمر: /search [كلمة البحث]\n\nمثال: /search أحمد\nأو: /search 123")
        return

    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة.")
        return

    search_term = " ".join(context.args)

    try:
        cursor = db_connection.cursor()

        # البحث في جدول Students مع JOINs
        cursor.execute("""
            SELECT TOP 10
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at,
                i.institution_name,
                b.branch_name,
                g.grade_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            WHERE s.is_active = 1 AND s.is_deleted = 0
            AND (s.student_name LIKE ?
                OR s.student_code LIKE ?
                OR s.student_phone LIKE ?
                OR s.father_phone LIKE ?
                OR s.mother_phone LIKE ?)
            ORDER BY s.student_code
        """, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

        results = cursor.fetchall()

        if results:
            columns = [column[0] for column in cursor.description]
            students_data = []
            for result in results:
                student_dict = dict(zip(columns, result))
                students_data.append(student_dict)

            message = f"🔍 نتائج البحث عن: \"{search_term}\"\n\n"
            message += format_students_list(students_data)
        else:
            message = f"🔍 لم يتم العثور على نتائج للبحث: \"{search_term}\"\n\n"
            message += "💡 جرب البحث بـ:\n"
            message += "- اسم الطالب\n"
            message += "- كود الطالب\n"
            message += "- رقم الهاتف"

        await update.message.reply_text(message)

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في البحث: {e}")
        print(f"❌ خطأ في البحث: {e}")

async def handle_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالج أزرار الموافقة/الرفض"""
    query = update.callback_query
    await query.answer()
    
    # التحقق من أن المستخدم هو الأدمن
    if query.from_user.id != ADMIN_ID:
        await query.edit_message_text("❌ غير مسموح لك بهذا الإجراء")
        return
    
    data = query.data
    
    if data.startswith('approve_'):
        user_id = int(data.replace('approve_', ''))
        
        # إضافة المستخدم للمفوضين
        authorized_users.add(user_id)
        
        # حفظ في قاعدة البيانات إذا كانت متصلة
        if db_connection:
            try:
                cursor = db_connection.cursor()
                user_info = pending_requests.get(user_id, {})
                cursor.execute("""
                    IF NOT EXISTS (SELECT * FROM telegram_bot_users WHERE telegram_id = ?)
                    INSERT INTO telegram_bot_users (telegram_id, username, first_name, last_name)
                    VALUES (?, ?, ?, ?)
                    ELSE
                    UPDATE telegram_bot_users SET is_active = 1, authorized_at = GETDATE() WHERE telegram_id = ?
                """, (user_id, user_id, user_info.get('username'), user_info.get('first_name'), user_info.get('last_name'), user_id))
                db_connection.commit()
            except Exception as e:
                logger.error(f"خطأ في حفظ المستخدم: {e}")
        
        updated_message = query.message.text + '\n\n✅ تمت الموافقة على المستخدم'
        await query.edit_message_text(updated_message)
        
        # إرسال إشعار للمستخدم
        if user_id in pending_requests:
            try:
                approval_message = """
🎉 تم قبول طلبك!

تم الموافقة على وصولك لبوت تيرا التعليمي.
يمكنك الآن استخدام الأمر /terra للوصول إلى جميع الخدمات.
"""
                await context.bot.send_message(chat_id=user_id, text=approval_message)
                del pending_requests[user_id]
            except Exception as e:
                logger.error(f"خطأ في إرسال إشعار الموافقة: {e}")
    
    elif data.startswith('reject_'):
        user_id = int(data.replace('reject_', ''))
        
        updated_message = query.message.text + '\n\n❌ تم رفض المستخدم'
        await query.edit_message_text(updated_message)
        
        # إرسال إشعار للمستخدم
        if user_id in pending_requests:
            try:
                rejection_message = """
😔 تم رفض طلبك

عذراً، لم تتم الموافقة على وصولك للبوت في الوقت الحالي.
يمكنك التواصل مع الإدارة للمزيد من المعلومات.
"""
                await context.bot.send_message(chat_id=user_id, text=rejection_message)
                del pending_requests[user_id]
            except Exception as e:
                logger.error(f"خطأ في إرسال إشعار الرفض: {e}")

    elif data.startswith("create_card_"):
        # إنشاء كارنيه للطالب
        student_code = data.replace("create_card_", "")

        try:
            # جلب بيانات الطالب
            cursor = db_connection.cursor()
            cursor.execute("""
                SELECT
                    s.student_code, s.student_name, s.student_phone,
                    i.institution_name, b.branch_name, g.grade_name
                FROM [dbo].[Students] s
                LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
                LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
                LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
                WHERE s.student_code = ? AND s.is_active = 1 AND s.is_deleted = 0
            """, (student_code,))

            student_data = cursor.fetchone()

            if student_data:
                columns = [column[0] for column in cursor.description]
                student_dict = dict(zip(columns, student_data))

                # إنشاء الكارنيه المحسن
                card_image = create_enhanced_student_card_image(student_dict)

                if card_image:
                    await query.edit_message_text("🆔 تم إنشاء الكارنيه الاحترافي بنجاح!")

                    # إرسال صورة الكارنيه المحسنة
                    await context.bot.send_photo(
                        chat_id=query.message.chat_id,
                        photo=card_image,
                        caption=f"""🆔 **كارنيه احترافي**

👤 **الطالب:** {student_dict['student_name']}
🆔 **الكود:** {student_code}
📚 **الصف:** {student_dict.get('grade_name', 'غير محدد')}
🌿 **الفرع:** {student_dict.get('branch_name', 'غير محدد')}

✨ تصميم احترافي مع QR Code كبير""",
                        parse_mode='Markdown'
                    )
                else:
                    await query.edit_message_text("❌ فشل في إنشاء الكارنيه. تأكد من تثبيت المكتبات المطلوبة:\n`pip install pillow qrcode`")
            else:
                await query.edit_message_text("❌ لم يتم العثور على بيانات الطالب")

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إنشاء الكارنيه: {e}")
            print(f"❌ خطأ في إنشاء الكارنيه: {e}")

    elif data.startswith("send_whatsapp_"):
        # معالجة إرسال الواتساب
        student_code = data.replace("send_whatsapp_", "")

        try:
            cursor = db_connection.cursor()
            cursor.execute("""
                SELECT
                    s.student_name, s.student_phone, s.student_code,
                    i.institution_name, b.branch_name, g.grade_name
                FROM [dbo].[Students] s
                LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
                LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
                LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
                WHERE s.student_code = ? AND s.is_active = 1 AND s.is_deleted = 0
            """, (student_code,))

            student_data = cursor.fetchone()

            if student_data:
                columns = [column[0] for column in cursor.description]
                student_dict = dict(zip(columns, student_data))

                # إنشاء الكارنيه أولاً
                card_image = create_enhanced_student_card_image(student_dict)

                # تنظيف رقم الهاتف
                phone = clean_phone_number(student_dict.get('student_phone', ''))

                # رسالة الواتساب بالإنجليزية
                whatsapp_message = f"""🎉 Congratulations! You have been registered in Terra Educational System

👤 Name: {student_dict.get('student_name', '')}
🆔 Student Code: {student_code}
📱 Phone: {student_dict.get('student_phone', '')}
🏫 Institution: {student_dict.get('institution_name', '')}
🌿 Branch: {student_dict.get('branch_name', '')}
📚 Grade: {student_dict.get('grade_name', '')}

🏫 Welcome to Terra Educational Family!
📚 We wish you success in your educational journey
🔔 You will receive notifications about schedules and activities soon"""

                # ترميز الرسالة
                import urllib.parse
                encoded_message = urllib.parse.quote(whatsapp_message, safe='')

                # إنشاء رابط الواتساب
                whatsapp_url = f"https://wa.me/{phone}?text={encoded_message}"

                # إرسال الكارنيه أولاً
                if card_image:
                    await context.bot.send_photo(
                        chat_id=query.message.chat_id,
                        photo=card_image,
                        caption=f"🆔 كارنيه الطالب: {student_dict.get('student_name', '')}"
                    )

                # إنشاء زر الواتساب
                keyboard = [[InlineKeyboardButton("📱 فتح الواتساب", url=whatsapp_url)]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    f"✅ تم إنشاء رسالة الواتساب والكارنيه للطالب: {student_dict.get('student_name', '')}\n\n"
                    f"📱 الرقم: {phone}\n\n"
                    f"🆔 تم إرسال الكارنيه أعلاه\n"
                    f"📱 اضغط على الزر أدناه لفتح الواتساب وإرسال الرسالة:",
                    reply_markup=reply_markup
                )
            else:
                await query.edit_message_text(f"❌ لم يتم العثور على الطالب: {student_code}")

        except Exception as e:
            await query.edit_message_text(f"❌ خطأ في إرسال الواتساب: {e}")
            print(f"❌ خطأ في إرسال الواتساب: {e}")

    elif data.startswith("menu_"):
        # معالجة أزرار القائمة الرئيسية
        if data == "menu_phone_search":
            # إنشاء واجهة إدخال رقم التليفون
            keyboard = [
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text("""
📱 **البحث بالتليفون**

أرسل رقم التليفون الذي تريد البحث عنه:

مثال:
• 01234567890
• 10
• 123

سيتم البحث في جميع أرقام الهواتف (الطالب، الأب، الأم)
""", reply_markup=reply_markup)

            # حفظ حالة المستخدم
            user_states[query.from_user.id] = "waiting_phone_number"

        elif data == "menu_card_extract":
            # إنشاء واجهة إدخال كود الطالب
            keyboard = [
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text("""
🆔 **استخراج كارنيه طالب**

أرسل كود الطالب أو الرقم:

أمثلة:
• STU001 (الكود الكامل)
• 1 (الرقم فقط)
• 25

سيتم إنشاء كارنيه احترافي مع QR Code كبير
""", reply_markup=reply_markup)

            # حفظ حالة المستخدم
            user_states[query.from_user.id] = "waiting_student_code"

        elif data == "menu_students":
            await query.edit_message_text("""
📊 **عرض الطلاب**

استخدم الأوامر التالية:
`/students` - عرض قائمة مختصرة
`/s` - عرض جميع الطلاب مفصل

سيتم عرض بيانات الطلاب مع المؤسسات والفروع
""", parse_mode='Markdown')

        elif data == "menu_stats":
            await query.edit_message_text("""
📈 **الإحصائيات**

استخدم الأمر التالي لعرض إحصائيات شاملة:
`/stats`

سيتم عرض:
• عدد الطلاب النشطين
• عدد المعلمين
• عدد المؤسسات والفروع
• إحصائيات الإشعارات
""", parse_mode='Markdown')

        elif data == "menu_search":
            await query.edit_message_text("""
🔍 **البحث العام**

استخدم الأمر التالي للبحث في أسماء الطلاب:
`/search [كلمة البحث]`

مثال:
`/search علي`
`/search محمد`

سيتم البحث في أسماء الطلاب
""", parse_mode='Markdown')

        elif data == "menu_latest":
            await query.edit_message_text("""
👤 **آخر طالب مسجل**

استخدم الأمر التالي لعرض آخر طالب تم تسجيله:
`/latest`

سيتم عرض بيانات آخر طالب مع جميع التفاصيل
""", parse_mode='Markdown')

        elif data == "menu_all_students":
            await query.edit_message_text("""
📋 **جميع الطلاب**

استخدم الأمر التالي لعرض جميع الطلاب:
`/s`

سيتم عرض جميع الطلاب مقسمين على رسائل متعددة
""", parse_mode='Markdown')

        elif data == "menu_chat_id":
            await query.edit_message_text("""
🆔 **معرف المحادثة**

استخدم الأمر التالي لعرض معرف المحادثة:
`/d`

مفيد لإضافة المحادثة لقائمة مستقبلي الإشعارات
""", parse_mode='Markdown')

    elif data == "back_to_main":
        # العودة للقائمة الرئيسية
        user_states.pop(query.from_user.id, None)  # حذف الحالة

        authorized_message = f"""
🎉 مرحباً!

أنت مفوض للوصول إلى نظام تيرا التعليمي.

🗄️ حالة قاعدة البيانات: متصل ✅
🌐 الخادم: terraaa.ddns.net:4100
📊 الطلاب النشطين: 72 طالب

استخدم الأزرار أدناه للوصول السريع للخدمات:
"""

        keyboard = create_main_menu_keyboard()
        await query.edit_message_text(authorized_message, reply_markup=keyboard)

async def handle_text_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالج الرسائل النصية للواجهة التفاعلية"""
    user_id = update.effective_user.id
    text = update.message.text.strip()

    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات

    # التحقق من حالة المستخدم
    if user_id not in user_states:
        return

    state = user_states[user_id]

    if state == "waiting_phone_number":
        # معالجة البحث بالتليفون
        await process_phone_search(update, text)
        user_states.pop(user_id, None)  # حذف الحالة

    elif state == "waiting_student_code":
        # معالجة استخراج الكارنيه
        await process_card_extraction(update, text)
        user_states.pop(user_id, None)  # حذف الحالة

async def process_phone_search(update: Update, phone_number: str):
    """معالجة البحث بالتليفون"""
    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة.")
        return

    try:
        cursor = db_connection.cursor()

        # البحث عن الطالب بالتليفون
        cursor.execute("""
            SELECT
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender, s.created_by,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at, s.updated_at,
                i.institution_name,
                b.branch_name,
                g.grade_name,
                gr.group_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            LEFT JOIN [dbo].[StudentGroups] sg ON s.id = sg.student_id
            LEFT JOIN [dbo].[Groups] gr ON sg.group_id = gr.id
            WHERE (s.student_phone LIKE ? OR s.father_phone LIKE ? OR s.mother_phone LIKE ?)
            AND s.is_active = 1 AND s.is_deleted = 0
            ORDER BY s.created_at DESC
        """, (f'%{phone_number}%', f'%{phone_number}%', f'%{phone_number}%'))

        students = cursor.fetchall()

        if students:
            columns = [column[0] for column in cursor.description]

            for student_row in students:
                student_dict = dict(zip(columns, student_row))

                # تنسيق رسالة الطالب
                message = format_student_details_with_phone(student_dict)

                # إنشاء أزرار الواتساب والكارنيه
                keyboard = create_student_action_buttons(student_dict)

                await update.message.reply_text(message, reply_markup=keyboard)
        else:
            await update.message.reply_text(f"❌ لم يتم العثور على طالب برقم التليفون: {phone_number}")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في البحث: {e}")

async def process_card_extraction(update: Update, student_code: str):
    """معالجة استخراج الكارنيه"""
    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة.")
        return

    # إذا كان الكود رقم فقط، أضف البادئة
    if student_code.isdigit():
        student_code = f"STU{student_code.zfill(3)}"

    try:
        cursor = db_connection.cursor()

        # البحث عن الطالب بالكود
        cursor.execute("""
            SELECT
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender, s.created_by,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at,
                i.institution_name,
                b.branch_name,
                g.grade_name,
                gr.group_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            LEFT JOIN [dbo].[StudentGroups] sg ON s.id = sg.student_id
            LEFT JOIN [dbo].[Groups] gr ON sg.group_id = gr.id
            WHERE s.student_code = ? AND s.is_active = 1 AND s.is_deleted = 0
        """, (student_code,))

        student_data = cursor.fetchone()

        if student_data:
            columns = [column[0] for column in cursor.description]
            student_dict = dict(zip(columns, student_data))

            # إرسال رسالة انتظار
            wait_message = await update.message.reply_text("🔄 جاري إنشاء الكارنيه الاحترافي...")

            # إنشاء الكارنيه المحسن
            card_image = create_enhanced_student_card_image(student_dict)

            if card_image:
                # حذف رسالة الانتظار
                await wait_message.delete()

                # إرسال الكارنيه
                caption = f"""🆔 كارنيه الطالب

👤 الاسم: {student_dict['student_name']}
🆔 الكود: {student_dict['student_code']}
📚 الصف: {student_dict.get('grade_name', 'غير محدد')}
🌿 الفرع: {student_dict.get('branch_name', 'غير محدد')}

✨ تم إنشاء الكارنيه بتصميم احترافي مع QR Code"""

                await update.message.reply_photo(photo=card_image, caption=caption)

                # إضافة أزرار إضافية
                keyboard = create_student_action_buttons(student_dict)
                await update.message.reply_text("استخدم الأزرار أدناه للمزيد من الخدمات:", reply_markup=keyboard)
            else:
                await wait_message.edit_text("❌ فشل في إنشاء الكارنيه. تأكد من تثبيت المكتبات المطلوبة.")
        else:
            await update.message.reply_text(f"❌ لم يتم العثور على طالب بالكود: {student_code}")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في استخراج الكارنيه: {e}")

async def monitor_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بدء مراقبة جدول الطلاب"""
    user_id = update.effective_user.id

    if user_id != ADMIN_ID:
        await update.message.reply_text("❌ هذا الأمر متاح للأدمن فقط")
        return

    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة")
        return

    global monitoring_active
    if monitoring_active:
        await update.message.reply_text("✅ المراقبة تعمل بالفعل")
        return

    await update.message.reply_text("🔍 بدء مراقبة جدول الطلاب...")

    # بدء المراقبة
    asyncio.create_task(monitor_students_table())

    await update.message.reply_text("✅ تم بدء مراقبة الطلاب الجدد!\n\n🔔 ستصلك إشعارات فورية عند إضافة طلاب جدد")

async def get_chat_id(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر /d لعرض معرف المحادثة وطلب إضافة للمستقبلين"""
    user = update.effective_user
    chat = update.effective_chat

    chat_id = chat.id
    chat_type = chat.type
    chat_name = chat.title if chat.title else f"{user.first_name} {user.last_name or ''}".strip()

    # عرض معلومات المحادثة
    info_message = f"""🆔 **معلومات المحادثة**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📱 **معرف المحادثة:** `{chat_id}`
👤 **نوع المحادثة:** {chat_type}
📝 **اسم المحادثة:** {chat_name}
👤 **المستخدم:** {user.first_name} (@{user.username or 'بدون معرف'})

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"""

    await update.message.reply_text(info_message, parse_mode='Markdown')

    # إرسال طلب للأدمن لإضافة هذه المحادثة للمستقبلين
    if chat_id != ADMIN_ID:
        admin_message = f"""📋 **طلب إضافة مستقبل جديد**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🆔 **معرف المحادثة:** `{chat_id}`
👤 **نوع المحادثة:** {chat_type}
📝 **اسم المحادثة:** {chat_name}
👤 **المستخدم:** {user.first_name} (@{user.username or 'بدون معرف'})

هل تريد إضافة هذه المحادثة لقائمة مستقبلي الإشعارات؟

/approve_{chat_id} - موافقة
/reject_{chat_id} - رفض"""

        try:
            await context.bot.send_message(
                chat_id=ADMIN_ID,
                text=admin_message,
                parse_mode='Markdown'
            )

            await update.message.reply_text("✅ تم إرسال طلبك للأدمن للموافقة على إضافتك لقائمة مستقبلي الإشعارات")

        except Exception as e:
            print(f"❌ خطأ في إرسال طلب للأدمن: {e}")

async def get_all_students(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر /s لعرض جميع الطلاب"""
    user_id = update.effective_user.id

    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات

    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة. تحقق من الاتصال بالخادم.")
        return

    try:
        cursor = db_connection.cursor()

        # جلب جميع الطلاب مع JOINs
        cursor.execute("""
            SELECT
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at,
                i.institution_name,
                b.branch_name,
                g.grade_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            WHERE s.is_active = 1 AND s.is_deleted = 0
            ORDER BY s.created_at DESC, s.student_code
        """)

        students = cursor.fetchall()

        if students:
            columns = [column[0] for column in cursor.description]
            students_data = [dict(zip(columns, student)) for student in students]

            # تقسيم الطلاب لرسائل متعددة (كل رسالة 10 طلاب)
            chunk_size = 10
            total_students = len(students_data)

            await update.message.reply_text(f"📊 **جميع الطلاب** ({total_students} طالب)\n{'='*50}")

            for i in range(0, len(students_data), chunk_size):
                chunk = students_data[i:i+chunk_size]
                message = f"📋 **الطلاب {i+1}-{min(i+chunk_size, total_students)}:**\n\n"

                for j, student in enumerate(chunk, i+1):
                    student_name = student.get('student_name', 'غير محدد')
                    student_code = student.get('student_code', 'غير محدد')
                    student_phone = student.get('student_phone', 'غير محدد')
                    institution_name = student.get('institution_name', 'غير محدد')
                    branch_name = student.get('branch_name', 'غير محدد')
                    grade_name = student.get('grade_name', 'غير محدد')
                    gender = student.get('gender', 'غير محدد')

                    message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    message += f"👤 **{student_name}** (كود: {student_code})\n"
                    message += f"📱 هاتف: {student_phone}\n"
                    message += f"🏫 مؤسسة: {institution_name}\n"
                    message += f"🌿 فرع: {branch_name}\n"
                    message += f"📚 صف: {grade_name}\n"
                    message += f"👫 جنس: {gender}\n\n"

                await update.message.reply_text(message, parse_mode='Markdown')

                # انتظار قصير بين الرسائل
                if i + chunk_size < len(students_data):
                    await asyncio.sleep(1)

        else:
            await update.message.reply_text("❌ لا توجد بيانات طلاب في قاعدة البيانات")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في جلب بيانات الطلاب: {e}")
        print(f"❌ خطأ في جلب جميع الطلاب: {e}")

async def find_student_by_phone(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر /phone للبحث عن طالب بالتليفون وإرسال واتساب وإنشاء كارنيه"""
    user_id = update.effective_user.id

    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات

    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة. تحقق من الاتصال بالخادم.")
        return

    # التحقق من وجود رقم التليفون
    if not context.args:
        await update.message.reply_text("📱 استخدم الأمر مع رقم التليفون:\n/phone 01234567890")
        return

    phone_number = context.args[0].strip()

    try:
        cursor = db_connection.cursor()

        # البحث عن الطالب بالتليفون (في جميع أرقام الهواتف)
        cursor.execute("""
            SELECT
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender, s.created_by,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at, s.updated_at,
                i.institution_name,
                b.branch_name,
                g.grade_name,
                gr.group_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            LEFT JOIN [dbo].[StudentGroups] sg ON s.id = sg.student_id
            LEFT JOIN [dbo].[Groups] gr ON sg.group_id = gr.id
            WHERE (s.student_phone LIKE ? OR s.father_phone LIKE ? OR s.mother_phone LIKE ?)
            AND s.is_active = 1 AND s.is_deleted = 0
            ORDER BY s.created_at DESC
        """, (f'%{phone_number}%', f'%{phone_number}%', f'%{phone_number}%'))

        students = cursor.fetchall()

        if students:
            columns = [column[0] for column in cursor.description]

            for student_row in students:
                student_dict = dict(zip(columns, student_row))

                # تنسيق رسالة الطالب
                message = format_student_details_with_phone(student_dict)

                # إنشاء أزرار الواتساب والكارنيه
                keyboard = create_student_action_buttons(student_dict)

                await update.message.reply_text(
                    message,
                    reply_markup=keyboard
                )
        else:
            await update.message.reply_text(f"❌ لم يتم العثور على طالب برقم التليفون: {phone_number}")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في البحث: {e}")
        print(f"❌ خطأ في البحث بالتليفون: {e}")

async def extract_student_card(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر /card لاستخراج كارنيه بكود الطالب"""
    user_id = update.effective_user.id

    # البوت مفتوح للجميع - لا حاجة لفحص الصلاحيات

    if not db_connection:
        await update.message.reply_text("❌ قاعدة البيانات غير متصلة. تحقق من الاتصال بالخادم.")
        return

    # التحقق من وجود كود الطالب
    if not context.args:
        await update.message.reply_text("""
🆔 **استخراج كارنيه طالب**

استخدم الأمر مع كود الطالب:
`/card STU001`
أو
`/card 1`

مثال:
`/card STU001` - للبحث بالكود الكامل
`/card 1` - للبحث بالرقم فقط
""", parse_mode='Markdown')
        return

    student_code = context.args[0].strip()

    # إذا كان الكود رقم فقط، أضف البادئة
    if student_code.isdigit():
        student_code = f"STU{student_code.zfill(3)}"

    try:
        cursor = db_connection.cursor()

        # البحث عن الطالب بالكود
        cursor.execute("""
            SELECT
                s.id, s.student_code, s.student_name, s.student_phone,
                s.father_phone, s.mother_phone, s.gender, s.created_by,
                s.is_exempt_from_fees, s.is_exempt_from_books, s.created_at,
                i.institution_name,
                b.branch_name,
                g.grade_name,
                gr.group_name
            FROM [dbo].[Students] s
            LEFT JOIN [dbo].[Institutions] i ON s.institution_id = i.id
            LEFT JOIN [dbo].[Branches] b ON s.branch_id = b.id
            LEFT JOIN [dbo].[Grades] g ON s.grade_id = g.id
            LEFT JOIN [dbo].[StudentGroups] sg ON s.id = sg.student_id
            LEFT JOIN [dbo].[Groups] gr ON sg.group_id = gr.id
            WHERE s.student_code = ? AND s.is_active = 1 AND s.is_deleted = 0
        """, (student_code,))

        student_data = cursor.fetchone()

        if student_data:
            columns = [column[0] for column in cursor.description]
            student_dict = dict(zip(columns, student_data))

            # إرسال رسالة انتظار
            wait_message = await update.message.reply_text("🔄 جاري إنشاء الكارنيه الاحترافي...")

            # إنشاء الكارنيه المحسن
            card_image = create_enhanced_student_card_image(student_dict)

            if card_image:
                # حذف رسالة الانتظار
                await wait_message.delete()

                # إرسال الكارنيه
                caption = f"""🆔 **كارنيه الطالب**

👤 **الاسم:** {student_dict['student_name']}
🆔 **الكود:** {student_dict['student_code']}
📚 **الصف:** {student_dict.get('grade_name', 'غير محدد')}
🌿 **الفرع:** {student_dict.get('branch_name', 'غير محدد')}

✨ تم إنشاء الكارنيه بتصميم احترافي مع QR Code"""

                await update.message.reply_photo(
                    photo=card_image,
                    caption=caption,
                    parse_mode='Markdown'
                )

                # إضافة أزرار إضافية
                keyboard = create_student_action_buttons(student_dict)
                await update.message.reply_text(
                    "استخدم الأزرار أدناه للمزيد من الخدمات:",
                    reply_markup=keyboard
                )
            else:
                await wait_message.edit_text("❌ فشل في إنشاء الكارنيه. تأكد من تثبيت المكتبات المطلوبة:\n`pip install pillow qrcode`")
        else:
            await update.message.reply_text(f"❌ لم يتم العثور على طالب بالكود: {student_code}")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في استخراج الكارنيه: {e}")
        print(f"❌ خطأ في استخراج الكارنيه: {e}")

def format_student_details_with_phone(student):
    """تنسيق تفاصيل الطالب مع أزرار الواتساب والكارنيه"""
    student_name = student.get('student_name', 'غير محدد')
    student_code = student.get('student_code', 'غير محدد')
    student_phone = student.get('student_phone', 'غير محدد')
    father_phone = student.get('father_phone', 'غير محدد')
    mother_phone = student.get('mother_phone', 'غير محدد')
    gender = student.get('gender', 'غير محدد')
    created_by = student.get('created_by', 'غير محدد')
    created_at = student.get('created_at', 'غير محدد')

    # البيانات الأكاديمية
    institution_name = student.get('institution_name', 'غير محدد')
    branch_name = student.get('branch_name', 'غير محدد')
    grade_name = student.get('grade_name', 'غير محدد')
    group_name = student.get('group_name', 'غير محدد')

    # الإعفاءات
    is_exempt_from_fees = student.get('is_exempt_from_fees', False)
    is_exempt_from_books = student.get('is_exempt_from_books', False)

    message = f"👤 تفاصيل الطالب\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

    message += f"👤 الاسم: {student_name}\n"
    message += f"🆔 كود الطالب: {student_code}\n"
    message += f"👫 الجنس: {gender}\n\n"

    message += f"📱 أرقام الهواتف:\n"
    message += f"   • الطالب: {student_phone}\n"
    message += f"   • الأب: {father_phone}\n"
    message += f"   • الأم: {mother_phone}\n\n"

    message += f"🏫 البيانات الأكاديمية:\n"
    message += f"   • المؤسسة: {institution_name}\n"
    message += f"   • الفرع: {branch_name}\n"
    message += f"   • الصف: {grade_name}\n"
    message += f"   • المجموعة: {group_name}\n\n"

    # الإعفاءات
    if is_exempt_from_fees or is_exempt_from_books:
        message += f"💰 الإعفاءات:\n"
        if is_exempt_from_fees:
            message += f"   ✅ معفى من الرسوم\n"
        if is_exempt_from_books:
            message += f"   ✅ معفى من الكتب\n"
        message += "\n"

    message += f"📅 تاريخ التسجيل: {created_at}\n"
    message += f"👤 أضيف بواسطة: {created_by}\n\n"

    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
    message += "استخدم الأزرار أدناه لإرسال واتساب أو إنشاء كارنيه"

    return message

def main():
    """الدالة الرئيسية"""
    global bot_application

    print("🤖 بدء تشغيل بوت تلجرام تيرا التعليمي")
    print(f"📱 معرف الأدمن: {ADMIN_ID}")
    print(f"🔑 التوكن: {BOT_TOKEN}")
    print()
    
    # محاولة الاتصال بقاعدة البيانات
    db_connected = connect_to_database()
    
    if db_connected:
        print("✅ البوت متصل بقاعدة البيانات SQL Server")
        setup_telegram_tables()
        # البوت مفتوح للجميع - لا حاجة لتحميل صلاحيات
    else:
        print("⚠️ البوت سيعمل بدون قاعدة بيانات")
        print("💡 تحقق من:")
        print("  - الاتصال بالإنترنت")
        print("  - صحة عنوان الخادم: terraaa.ddns.net:4100")
        print("  - تثبيت ODBC Driver 17 for SQL Server")
        print("  - صحة بيانات الاتصال")

    print("⚡ البوت جاهز لاستقبال الرسائل...")
    print()

    # إنشاء التطبيق
    application = Application.builder().token(BOT_TOKEN).build()
    bot_application = application  # حفظ مرجع للتطبيق

    # إضافة معالجات الأوامر
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("terra", terra))
    application.add_handler(CommandHandler("students", students))
    application.add_handler(CommandHandler("stats", stats))
    application.add_handler(CommandHandler("latest", latest))
    application.add_handler(CommandHandler("search", search))
    application.add_handler(CommandHandler("monitor", monitor_start))
    application.add_handler(CommandHandler("d", get_chat_id))
    application.add_handler(CommandHandler("s", get_all_students))
    application.add_handler(CommandHandler("phone", find_student_by_phone))
    application.add_handler(CommandHandler("card", extract_student_card))
    application.add_handler(CallbackQueryHandler(handle_callback))

    # إضافة معالج الرسائل النصية للواجهة التفاعلية
    from telegram.ext import MessageHandler, filters
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text_message))

    # تشغيل البوت
    print("🔄 بدء تشغيل البوت...")

    if db_connected:
        print("🔍 سيتم بدء مراقبة جدول الطلاب بعد تشغيل البوت...")

    try:
        application.run_polling(
            poll_interval=1.0,
            timeout=10
        )
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        raise

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n🔄 إيقاف البوت...")
        if db_connection:
            db_connection.close()
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        if db_connection:
            db_connection.close()
